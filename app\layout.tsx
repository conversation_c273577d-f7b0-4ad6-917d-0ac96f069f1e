import type React from "react"
import "./globals.css"
import { Providers } from "./providers"
import { Toaster } from "@/components/ui/toaster"

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en">
      <body>
        <Providers>
          {children}
          <Toaster />
        </Providers>
      </body>
    </html>
  )
}

export const metadata = {
  title: 'Majestic Capital - Staking Platform',
  description: 'Stake your tokens and earn guaranteed returns with Majestic Capital',
  generator: 'v0.dev',
  viewport: {
    width: 'device-width',
    initialScale: 1,
    maximumScale: 1,
    userScalable: false,
    viewportFit: 'cover',
  },
  themeColor: [
    { media: '(prefers-color-scheme: light)', color: '#f59e0b' },
    { media: '(prefers-color-scheme: dark)', color: '#1e293b' }
  ],
  appleWebApp: {
    capable: true,
    statusBarStyle: 'default',
    title: 'Majestic Capital'
  }
};