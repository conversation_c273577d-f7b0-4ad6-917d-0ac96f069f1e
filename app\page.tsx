"use client"

import { useState } from "react"
import { ConnectButton } from "@rainbow-me/rainbowkit"
import { useAccount } from "wagmi"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import { Badge } from "@/components/ui/badge"
import { useToast } from "@/hooks/use-toast"
import { useStaking } from "@/hooks/useStaking"
import { formatUnits } from "viem"
import {
  LayoutDashboard,
  Coins,
  ArrowRightLeft,
  CreditCard,
  Settings,
  LogOut,
  Menu,
  DollarSign,
  Calendar,
  Clock,
  CheckCircle,
  Wallet,
  User,
  ChevronDown,
} from "lucide-react"
import { ThemeToggle } from "@/components/theme-toggle"
import Image from "next/image"

export default function MajesticCapitalStaking() {
  const { address, isConnected } = useAccount()
  const { toast } = useToast()
  const {
    userStakes,
    usdtBalance,
    usdcBalance,
    usdtAllowance,
    usdcAllowance,
    totalAssets,
    availableAssets,
    stakedTokens,
    createStake,
    approveToken,
    claimStake,
    emergencyWithdraw,
    canClaimStake,
    calculatePotentialEarnings,
    isPending,
    isConfirming,
    isConfirmed,
    error,
    refetchStakes,
  } = useStaking()

   const [isStakeModalOpen, setIsStakeModalOpen] = useState(false)
  const [stakeAmount, setStakeAmount] = useState("")
  const [selectedToken, setSelectedToken] = useState<"USDT" | "USDC">("USDT")
  const [selectedPlan, setSelectedPlan] = useState("")

  const sidebarItems = [
    { icon: LayoutDashboard, label: "Dashboard", active: false },
    { icon: Coins, label: "Staking", active: true },
    { icon: ArrowRightLeft, label: "Swap", active: false },
    { icon: CreditCard, label: "SEPA", active: false },
    { icon: Settings, label: "Profile Setting", active: false },
  ]

    const stakingPlans = [
    { period: "3", rate: "18", label: "3 Months - 18% APY" },
    { period: "6", rate: "24", label: "6 Months - 24% APY" },
    { period: "12", rate: "30", label: "12 Months - 30% APY" },
  ]

 const handleStake = async () => {
    if (!stakeAmount || !selectedPlan) {
      toast({
        title: "Error",
        description: "Please fill in all fields",
        variant: "destructive",
      })
      return
    }

    if (Number(stakeAmount) < 10) {
      toast({
        title: "Error",
        description: "Minimum stake amount is $100",
        variant: "destructive",
      })
      return
    }

    try {
      const currentAllowance = selectedToken === "USDT" ? usdtAllowance : usdcAllowance
      const currentBalance = selectedToken === "USDT" ? usdtBalance : usdcBalance

      // Check if user has enough balance
      if (Number(currentBalance) < Number(stakeAmount)) {
        toast({
          title: "Insufficient Balance",
          description: `You don't have enough ${selectedToken} to stake`,
          variant: "destructive",
        })
        return
      }

      // Check if approval is needed
      if (Number(currentAllowance) < Number(stakeAmount)) {
        toast({
          title: "Approving token...",
          description: "Please confirm the approval transaction",
        })
        
        await approveToken(selectedToken, stakeAmount)
        
        // Wait for approval to complete before proceeding
        toast({
          title: "Approval successful!",
          description: "Now you can proceed with staking. Please try again.",
        })
        return
      }

      toast({
        title: "Creating stake...",
        description: "Please confirm the staking transaction",
      })

      await createStake(selectedToken, stakeAmount, Number(selectedPlan))

      setIsStakeModalOpen(false)
      setStakeAmount("")
      setSelectedPlan("")

      toast({
        title: "Stake created successfully!",
        description: `Staked ${stakeAmount} ${selectedToken} for ${selectedPlan} months`,
      })
    } catch (error: any) {
      console.error("Staking error:", error)
      toast({
        title: "Error",
        description: error.message || "Failed to create stake. Please try again.",
        variant: "destructive",
      })
    }
  }

  const handleClaim = async (stakeId: number) => {
    try {
      await claimStake(stakeId)
      toast({
        title: "Claiming stake...",
        description: "Please confirm the transaction",
      })
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to claim stake. Please try again.",
        variant: "destructive",
      })
    }
  }

  const formatDate = (timestamp: bigint) => {
    return new Date(Number(timestamp) * 1000).toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
    })
  }



  const getTokenSymbol = (tokenAddress: string) => {
    return tokenAddress.toLowerCase() === "0xdac17f958d2ee523a2206206994597c13d831ec7" ? "USDT" : "USDC"
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-white via-amber-50 to-amber-100 dark:from-slate-900 dark:via-purple-900 dark:to-slate-900">
      {/* Header */}
      <header className="border-b border-amber-200/50 dark:border-slate-700/50 bg-white/50 dark:bg-slate-900/50 backdrop-blur-sm">
        <div className="flex items-center justify-between px-6 py-4">
          <div className="flex items-center gap-4">
            <div className="flex items-center gap-3">
              <Image
                src="/logo.png"
                alt="Majestic Capital Logo"
                width={40}
                height={40}
                className="rounded-full  object-cover"
                priority
              />
              <span className="text-white font-semibold text-lg">Majestic Capital</span>
            </div>
            <Button variant="ghost" size="sm" className="text-slate-400 hover:text-white lg:hidden">
              <Menu className="h-5 w-5" />
            </Button>
          </div>

          <div className="flex items-center gap-4">
           
            <ConnectButton />
           
            <ThemeToggle />
            <Button
              variant="ghost"
              size="sm"
              className="text-amber-700 dark:text-slate-400 hover:text-amber-800 dark:hover:text-white"
            >
              <User className="h-5 w-5" />
              <ChevronDown className="h-4 w-4 ml-1" />
            </Button>
          </div>
        </div>
      </header>

      <div className="flex">
        {/* Sidebar */}
        <aside className="w-64 bg-white/30 dark:bg-slate-900/30 backdrop-blur-sm border-r border-amber-200/50 dark:border-slate-700/50 min-h-[calc(100vh-73px)]">
          <nav className="p-4 space-y-2">
            {sidebarItems.map((item, index) => (
              <Button
                key={index}
                variant={item.active ? "secondary" : "ghost"}
                className={`w-full justify-start gap-3 ${
                  item.active
                    ? "bg-amber-200 dark:bg-slate-800 text-amber-900 dark:text-white"
                    : "text-amber-700 dark:text-slate-400 hover:text-amber-900 dark:hover:text-white hover:bg-amber-100/50 dark:hover:bg-slate-800/50"
                }`}
              >
                <item.icon className="h-5 w-5" />
                {item.label}
              </Button>
            ))}
          </nav>

          <div className="absolute bottom-4 left-4 right-4">
            <Button
              variant="ghost"
              className="w-full justify-start gap-3 text-amber-700 dark:text-slate-400 hover:text-amber-900 dark:hover:text-white hover:bg-amber-100/50 dark:hover:bg-slate-800/50"
            >
              <LogOut className="h-5 w-5" />
              Logout
            </Button>
          </div>
        </aside>

        {/* Main Content */}
        <main className="flex-1 p-6">
          <div className="max-w-6xl mx-auto space-y-6">
            <h1 className="text-3xl font-bold text-amber-900 dark:text-white">My Assets</h1>

            {/* Asset Overview Cards */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <Card className="bg-white/50 dark:bg-slate-800/50 border-amber-200 dark:border-slate-700 backdrop-blur-sm">
                <CardHeader className="pb-3">
                  <CardTitle className="text-amber-700 dark:text-slate-300 text-sm font-medium">Total Assets</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="flex items-center gap-3">
                    <div className="w-12 h-12 rounded-lg bg-amber-500 dark:bg-blue-600 flex items-center justify-center">
                      <div className="w-6 h-6 rounded-full bg-amber-300 dark:bg-blue-400"></div>
                    </div>
                    <span className="text-3xl font-bold text-amber-900 dark:text-white">${totalAssets}</span>
                  </div>
                </CardContent>
              </Card>

              <Card className="bg-white/50 dark:bg-slate-800/50 border-amber-200 dark:border-slate-700 backdrop-blur-sm">
                <CardHeader className="pb-3">
                  <CardTitle className="text-amber-700 dark:text-slate-300 text-sm font-medium">
                    Available Assets
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-center gap-3">
                    <div className="w-12 h-12 rounded-lg bg-amber-500 dark:bg-blue-600 flex items-center justify-center">
                      <div className="w-6 h-6 rounded-full bg-amber-300 dark:bg-blue-400"></div>
                    </div>
                    <span className="text-3xl font-bold text-amber-900 dark:text-white">${availableAssets}</span>
                  </div>
                  {isConnected && (
                    <div className="text-xs text-slate-400 space-y-1">
                      <div>USDT: {Number(usdtBalance).toFixed(2)}</div>
                      <div>USDC: {Number(usdcBalance).toFixed(2)}</div>
                    </div>
                  )}
                </CardContent>
              </Card>

              <Card className="bg-white/50 dark:bg-slate-800/50 border-amber-200 dark:border-slate-700 backdrop-blur-sm">
                <CardHeader className="pb-3">
                  <CardTitle className="text-amber-700 dark:text-slate-300 text-sm font-medium">
                    Staked Tokens
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="flex items-center gap-3">
                    <div className="w-12 h-12 rounded-lg bg-amber-500 dark:bg-blue-600 flex items-center justify-center">
                      <div className="w-6 h-6 rounded-full bg-amber-300 dark:bg-blue-400"></div>
                    </div>
                    <span className="text-3xl font-bold text-amber-900 dark:text-white">${stakedTokens}</span>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Staking Plans */}
            {isConnected && (
              <>
                <div className="text-center space-y-4">
                  <h2 className="text-2xl font-bold text-amber-900 dark:text-white">Staking Plans</h2>
                  <p className="text-amber-700 dark:text-slate-300">
                    Choose your preferred staking plan and earn guaranteed returns
                  </p>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  {stakingPlans.map((plan) => (
                    <Card
                      key={plan.period}
                      className="bg-white/50 dark:bg-slate-800/50 border-amber-200 dark:border-slate-700 backdrop-blur-sm hover:bg-white/70 dark:hover:bg-slate-800/70 transition-all"
                    >
                      <CardHeader className="text-center">
                        <CardTitle className="text-amber-900 dark:text-white text-xl">{plan.period} Months</CardTitle>
                        <div className="text-3xl font-bold text-gradient bg-gradient-to-r from-amber-400 to-orange-400 bg-clip-text text-transparent">
                          {plan.rate}% APY
                        </div>
                      </CardHeader>
                      <CardContent className="space-y-4">
                        <div className="space-y-2 text-sm text-amber-700 dark:text-slate-300">
                          <div className="flex justify-between">
                            <span>Lock Period:</span>
                            <span className="text-amber-900 dark:text-white">{plan.period} months</span>
                          </div>
                          <div className="flex justify-between">
                            <span>Interest Rate:</span>
                            <span className="text-green-600 dark:text-green-400">{plan.rate}% per year</span>
                          </div>
                          <div className="flex justify-between">
                            <span>Minimum:</span>
                            <span className="text-amber-900 dark:text-white">$100</span>
                          </div>
                        </div>
                        <Dialog open={isStakeModalOpen} onOpenChange={setIsStakeModalOpen}>
                          <DialogTrigger asChild>
                            <Button
                              className="w-full bg-gradient-to-r from-amber-500 to-orange-500 hover:from-amber-600 hover:to-orange-600 text-white"
                              onClick={() => setSelectedPlan(plan.period)}
                            >
                              Stake Now
                            </Button>
                          </DialogTrigger>
                        </Dialog>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </>
            )}

            {/* Staking Modal */}
            <Dialog open={isStakeModalOpen} onOpenChange={setIsStakeModalOpen}>
              <DialogContent className="bg-white dark:bg-slate-800 border-amber-200 dark:border-slate-700 text-amber-900 dark:text-white max-w-md">
                <DialogHeader>
                  <DialogTitle>Stake Tokens</DialogTitle>
                </DialogHeader>
                <div className="space-y-6">
                  <div>
                    <Label htmlFor="token">Select Token</Label>
                    <Select value={selectedToken} onValueChange={(value: "USDT" | "USDC") => setSelectedToken(value)}>
                      <SelectTrigger className="bg-amber-50 dark:bg-slate-700 border-amber-200 dark:border-slate-600 text-amber-900 dark:text-white">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent className="bg-slate-700 border-slate-600">
                        <SelectItem value="USDT">USDT (Balance: {Number(usdtBalance).toFixed(2)})</SelectItem>
                        <SelectItem value="USDC">USDC (Balance: {Number(usdcBalance).toFixed(2)})</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div>
                    <Label htmlFor="amount">Amount to Stake</Label>
                    <Input
                      id="amount"
                      type="number"
                      placeholder="Enter amount (min $100)"
                      value={stakeAmount}
                      onChange={(e) => setStakeAmount(e.target.value)}
                      className="bg-amber-50 dark:bg-slate-700 border-amber-200 dark:border-slate-600 text-amber-900 dark:text-white"
                    />
                  </div>

                  {stakeAmount && selectedPlan && (
                    <div className="bg-amber-100/50 dark:bg-slate-700/50 p-4 rounded-lg space-y-2">
                      <h4 className="font-semibold text-amber-800 dark:text-amber-400">Staking Summary</h4>
                      <div className="text-sm space-y-1">
                        <div className="flex justify-between">
                          <span>Amount:</span>
                          <span>
                            {stakeAmount} {selectedToken}
                          </span>
                        </div>
                        <div className="flex justify-between">
                          <span>Period:</span>
                          <span>{selectedPlan} months</span>
                        </div>
                        <div className="flex justify-between">
                          <span>APY:</span>
                          <span>{stakingPlans.find((p) => p.period === selectedPlan)?.rate}%</span>
                        </div>
                      </div>
                    </div>
                  )}

                  <Button
                    className="w-full bg-gradient-to-r from-amber-500 to-orange-500 hover:from-amber-600 hover:to-orange-600"
                    onClick={handleStake}
                    disabled={!stakeAmount || Number(stakeAmount) < 10 || isPending || isConfirming}
                  >
                    {isPending || isConfirming ? "Processing..." : "Confirm Stake"}
                  </Button>
                </div>
              </DialogContent>
            </Dialog>

            {/* My Stakes */}
            {isConnected && userStakes.length > 0 && (
              <Card className="bg-white/50 dark:bg-slate-800/50 border-amber-200 dark:border-slate-700 backdrop-blur-sm">
                <CardHeader>
                  <CardTitle className="text-white">My Stakes</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="overflow-x-auto">
                    <table className="w-full">
                      <thead>
                        <tr className="border-b border-slate-700">
                          <th className="text-left py-3 px-4 text-amber-600 dark:text-slate-400 font-medium">Amount</th>
                          <th className="text-left py-3 px-4 text-slate-400 font-medium">Starting Date</th>
                          <th className="text-left py-3 px-4 text-slate-400 font-medium">Lock Period</th>
                          <th className="text-left py-3 px-4 text-slate-400 font-medium">Interest/Year</th>
                          <th className="text-left py-3 px-4 text-slate-400 font-medium">End Date</th>
                          <th className="text-left py-3 px-4 text-slate-400 font-medium">Actions</th>
                        </tr>
                      </thead>
                      <tbody>
                        {userStakes.map((stake, index) => (
                          <tr key={index} className="border-b border-slate-700/50">
                            <td className="py-4 px-4">
                              <div className="flex items-center gap-2">
                                <DollarSign className="h-4 w-4 text-green-400" />
                                <span className="text-amber-900 dark:text-white font-medium">
                                  {formatUnits(stake.amount, 6)} {getTokenSymbol(stake.token)}
                                </span>
                              </div>
                            </td>
                            <td className="py-4 px-4">
                              <div className="flex items-center gap-2">
                                <Calendar className="h-4 w-4 text-blue-400" />
                                <span className="text-amber-700 dark:text-slate-300">
                                  {formatDate(stake.startDate)}
                                </span>
                              </div>
                            </td>
                            <td className="py-4 px-4">
                              <div className="flex items-center gap-2">
                                <Clock className="h-4 w-4 text-orange-400" />
                                <span className="text-amber-900 dark:text-white">
                                  {Number(stake.lockPeriod) / (24 * 60 * 60) === 90
                                    ? "3"
                                    : Number(stake.lockPeriod) / (24 * 60 * 60) === 180
                                      ? "6"
                                      : "12"}{" "}
                                  months
                                </span>
                              </div>
                            </td>
                            <td className="py-4 px-4">
                              <span className="text-green-400 font-medium">{Number(stake.interestRate) / 100}%</span>
                            </td>
                            <td className="py-4 px-4">
                              <span className="text-slate-300">{formatDate(stake.endDate)}</span>
                            </td>
                            <td className="py-4 px-4">
                              {stake.claimed ? (
                                <Badge variant="secondary" className="bg-gray-900/30 text-gray-400 border-gray-700">
                                  <CheckCircle className="h-3 w-3 mr-1" />
                                  Claimed
                                </Badge>
                              ) : canClaimStake(stake.endDate) ? (
                                <Button
                                  size="sm"
                                  onClick={() => handleClaim(index)}
                                  className="bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white"
                                  disabled={isPending || isConfirming}
                                >
                                  Claim
                                </Button>
                              ) : (
                                <Badge variant="secondary" className="bg-blue-900/30 text-blue-400 border-blue-700">
                                  <Clock className="h-3 w-3 mr-1" />
                                  Locked
                                </Badge>
                              )}
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Connect Wallet Prompt */}
            {!isConnected && (
              <Card className="bg-white/50 dark:bg-slate-800/50 border-amber-200 dark:border-slate-700 backdrop-blur-sm">
                <CardContent className="text-center py-12">
                  <Wallet className="h-12 w-12 text-slate-400 mx-auto mb-4" />
                  <h3 className="text-xl font-semibold text-amber-900 dark:text-white mb-2">Connect Your Wallet</h3>
                  <p className="text-amber-700 dark:text-slate-400 mb-6">
                    Connect your wallet to start staking and earning rewards
                  </p>
                  <ConnectButton />
                </CardContent>
              </Card>
            )}
          </div>
        </main>
      </div>
    </div>
  )
}
