"use client"

import { useAccount, useReadContract, useWriteContract, useWaitForTransactionReceipt } from "wagmi"
import { parseUnits, formatUnits } from "viem"
import { STAKING_CONTRACT_ADDRESS, STAKING_ABI, ERC20_ABI, USDT_ADDRESS, USDC_ADDRESS } from "@/lib/contracts"
import { useEffect } from "react"

export interface Stake {
  amount: bigint
  startDate: bigint
  lockPeriod: bigint
  interestRate: bigint
  endDate: bigint
  token: string
  claimed: boolean
}

export function useStaking() {
  const { address } = useAccount()
  const { writeContractAsync, data: hash, isPending, error } = useWriteContract()
  const { isLoading: isConfirming, isSuccess: isConfirmed } = useWaitForTransactionReceipt({
    hash,
  })

  // Get user stakes
  const { data: userStakes, refetch: refetchStakes } = useReadContract({
    address: STAKING_CONTRACT_ADDRESS,
    abi: STAKING_ABI,
    functionName: "getUserStakes",
    args: address ? [address] : undefined,
    query: {
      enabled: !!address,
    },
  }) as { data: Stake[] | undefined; refetch: () => void }

  // Get USDT balance
  const { data: usdtBalance, refetch: refetchUsdtBalance } = useReadContract({
    address: USDT_ADDRESS,
    abi: ERC20_ABI,
    functionName: "balanceOf",
    args: address ? [address] : undefined,
    query: {
      enabled: !!address,
    },
  })

  // Get USDC balance
  const { data: usdcBalance, refetch: refetchUsdcBalance } = useReadContract({
    address: USDC_ADDRESS,
    abi: ERC20_ABI,
    functionName: "balanceOf",
    args: address ? [address] : undefined,
    query: {
      enabled: !!address,
    },
  })

  // Get USDT allowance
  const { data: usdtAllowance, refetch: refetchUsdtAllowance } = useReadContract({
    address: USDT_ADDRESS,
    abi: ERC20_ABI,
    functionName: "allowance",
    args: address ? [address, STAKING_CONTRACT_ADDRESS] : undefined,
    query: {
      enabled: !!address,
    },
  })

  // Get USDC allowance
  const { data: usdcAllowance, refetch: refetchUsdcAllowance } = useReadContract({
    address: USDC_ADDRESS,
    abi: ERC20_ABI,
    functionName: "allowance",
    args: address ? [address, STAKING_CONTRACT_ADDRESS] : undefined,
    query: {
      enabled: !!address,
    },
  })

  const createStake = async (token: "USDT" | "USDC", amount: string, lockPeriod: number) => {
    if (!address) throw new Error("Wallet not connected")
    
    const tokenAddress = token === "USDT" ? USDT_ADDRESS : USDC_ADDRESS
    const amountWei = parseUnits(amount, 6) // USDT and USDC have 6 decimals
    
    // Convert lock period to seconds based on the contract constants
    let lockPeriodSeconds: bigint
    switch (lockPeriod) {
      case 3:
        lockPeriodSeconds = BigInt(90 * 24 * 60 * 60) // 90 days
        break
      case 6:
        lockPeriodSeconds = BigInt(180 * 24 * 60 * 60) // 180 days
        break
      case 12:
        lockPeriodSeconds = BigInt(365 * 24 * 60 * 60) // 365 days
        break
      default:
        throw new Error("Invalid lock period")
    }

    // Validate amount
    const minStake = parseUnits("100", 6) // $100 minimum
    if (amountWei < minStake) {
      throw new Error("Minimum stake is $100")
    }

    // Check balance
    const currentBalance = token === "USDT" ? usdtBalance : usdcBalance
    if (!currentBalance || amountWei > currentBalance) {
      throw new Error("Insufficient balance")
    }

    // Check allowance
    const currentAllowance = token === "USDT" ? usdtAllowance : usdcAllowance
    if (!currentAllowance || amountWei > currentAllowance) {
      throw new Error("Insufficient allowance. Please approve tokens first.")
    }

    return writeContractAsync({
      address: STAKING_CONTRACT_ADDRESS,
      abi: STAKING_ABI,
      functionName: "createStake",
      args: [tokenAddress, amountWei, lockPeriodSeconds],
    })
  }

  const approveToken = async (token: "USDT" | "USDC", amount: string) => {
    if (!address) throw new Error("Wallet not connected")
    
    const tokenAddress = token === "USDT" ? USDT_ADDRESS : USDC_ADDRESS
    const amountWei = parseUnits(amount, 6)

    // For USDT, we need to set allowance to 0 first due to its implementation
    if (token === "USDT") {
      const currentAllowance = usdtAllowance
      if (currentAllowance && currentAllowance > BigInt(0)) {
        // First set allowance to 0
         writeContractAsync({
          address: tokenAddress,
          abi: ERC20_ABI,
          functionName: "approve",
          args: [STAKING_CONTRACT_ADDRESS, BigInt(0)],
        })
      }
    }

    return writeContractAsync({
      address: tokenAddress,
      abi: ERC20_ABI,
      functionName: "approve",
      args: [STAKING_CONTRACT_ADDRESS, amountWei],
    })
  }

  const claimStake = async (stakeId: number) => {
    if (!address) throw new Error("Wallet not connected")
    
    if (!userStakes || stakeId >= userStakes.length) {
      throw new Error("Invalid stake ID")
    }

    const stake = userStakes[stakeId]
    if (stake.claimed) {
      throw new Error("Stake already claimed")
    }

    if (Date.now() / 1000 < Number(stake.endDate)) {
      throw new Error("Stake is still locked")
    }

    return writeContractAsync({
      address: STAKING_CONTRACT_ADDRESS,
      abi: STAKING_ABI,
      functionName: "claimStake",
      args: [BigInt(stakeId)],
    })
  }

  const emergencyWithdraw = async (stakeId: number) => {
    if (!address) throw new Error("Wallet not connected")
    
    if (!userStakes || stakeId >= userStakes.length) {
      throw new Error("Invalid stake ID")
    }

    const stake = userStakes[stakeId]
    if (stake.claimed) {
      throw new Error("Stake already claimed")
    }

    return writeContractAsync({
      address: STAKING_CONTRACT_ADDRESS,
      abi: STAKING_ABI,
      functionName: "emergencyWithdraw",
      args: [BigInt(stakeId)],
    })
  }

  // Calculate totals with proper error handling
  const calculateTotals = () => {
    if (!address) {
      return {
        totalAssets: "0.00",
        availableAssets: "0.00",
        stakedTokens: "0.00",
        usdtBalanceFormatted: "0.00",
        usdcBalanceFormatted: "0.00",
      }
    }

    try {
      // Format wallet balances with null checks
      const usdtBalanceFormatted = usdtBalance ? Number(formatUnits(usdtBalance, 6)) : 0
      const usdcBalanceFormatted = usdcBalance ? Number(formatUnits(usdcBalance, 6)) : 0

      // Calculate staked amount from active stakes
      const stakedAmount = userStakes
        ? userStakes.reduce((total, stake) => {
            if (!stake.claimed) {
              return total + Number(formatUnits(stake.amount, 6))
            }
            return total
          }, 0)
        : 0

      const availableAssets = usdtBalanceFormatted + usdcBalanceFormatted
      const totalAssets = availableAssets + stakedAmount

      return {
        totalAssets: totalAssets.toFixed(2),
        availableAssets: availableAssets.toFixed(2),
        stakedTokens: stakedAmount.toFixed(2),
        usdtBalanceFormatted: usdtBalanceFormatted.toFixed(2),
        usdcBalanceFormatted: usdcBalanceFormatted.toFixed(2),
      }
    } catch (error) {
      console.error("Error calculating totals:", error)
      return {
        totalAssets: "0.00",
        availableAssets: "0.00",
        stakedTokens: "0.00",
        usdtBalanceFormatted: "0.00",
        usdcBalanceFormatted: "0.00",
      }
    }
  }

  // Refetch all data when transaction is confirmed
  useEffect(() => {
    if (isConfirmed) {
      refetchStakes()
      refetchUsdtBalance()
      refetchUsdcBalance()
      refetchUsdtAllowance()
      refetchUsdcAllowance()
    }
  }, [isConfirmed, refetchStakes, refetchUsdtBalance, refetchUsdcBalance, refetchUsdtAllowance, refetchUsdcAllowance])

  const totals = calculateTotals()

  // Helper function to check if stake can be claimed
  const canClaimStake = (stake: Stake) => {
    return !stake.claimed && Date.now() / 1000 >= Number(stake.endDate)
  }

  // Helper function to calculate potential earnings
  const calculatePotentialEarnings = (stake: Stake) => {
    const principal = Number(formatUnits(stake.amount, 6))
    const interestRate = Number(stake.interestRate) / 10000 // Convert from basis points
    const interest = principal * interestRate
    return {
      principal,
      interest,
      total: principal + interest
    }
  }

  return {
    userStakes: userStakes || [],
    usdtBalance: usdtBalance ? formatUnits(usdtBalance, 6) : "0",
    usdcBalance: usdcBalance ? formatUnits(usdcBalance, 6) : "0",
    usdtAllowance: usdtAllowance ? formatUnits(usdtAllowance, 6) : "0",
    usdcAllowance: usdcAllowance ? formatUnits(usdcAllowance, 6) : "0",
    createStake,
    approveToken,
    claimStake,
    emergencyWithdraw,
    canClaimStake,
    calculatePotentialEarnings,
    isPending,
    isConfirming,
    isConfirmed,
    error,
    refetchStakes,
    ...totals,
  }
}