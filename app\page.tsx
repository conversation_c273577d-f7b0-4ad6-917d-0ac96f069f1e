"use client"

import { useState, useEffect } from "react"
import { ConnectButton } from "@rainbow-me/rainbowkit"
import { useAccount } from "wagmi"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import { Badge } from "@/components/ui/badge"
import { useToast } from "@/hooks/use-toast"
import { useStaking } from "@/hooks/useStaking"
import { formatUnits } from "viem"
import {
  LayoutDashboard,
  Coins,
  ArrowRightLeft,
  CreditCard,
  Settings,
  Menu,
  DollarSign,
  Calendar,
  Clock,
  CheckCircle,
  Wallet,
  User,
  ChevronDown,
  PiggyBank,
  Lock,
  TrendingUp,
  Home,
} from "lucide-react"
import { ThemeToggle } from "@/components/theme-toggle"
import Image from "next/image"
import Link from "next/link"

export default function MajesticCapitalStaking() {
  const { address, isConnected } = useAccount()
  const { toast } = useToast()
  const {
    userStakes,
    usdtBalance,
    usdcBalance,
    usdtAllowance,
    usdcAllowance,
    totalAssets,
    availableAssets,
    stakedTokens,
    createStake,
    approveToken,
    claimStake,
    emergencyWithdraw,
    canClaimStake,
    calculatePotentialEarnings,
    isPending,
    isConfirming,
    isConfirmed,
    error,
    refetchStakes,
    hash,
  } = useStaking()

  const [isStakeModalOpen, setIsStakeModalOpen] = useState(false)
  const [stakeAmount, setStakeAmount] = useState("")
  const [selectedToken, setSelectedToken] = useState<"USDT" | "USDC">("USDT")
  const [selectedPlan, setSelectedPlan] = useState("")
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false)
  const [lastTransactionData, setLastTransactionData] = useState<{
    type: 'stake' | 'approve' | 'claim'
    data: any
  } | null>(null)

  const sidebarItems = [
    { icon: Home, label: "Home", active: false, href: "/" },
    { icon: LayoutDashboard, label: "Dashboard", active: false },
    { icon: Coins, label: "Staking", active: true },
    { icon: Settings, label: "Profile Setting", active: false },
  ]

  const stakingPlans = [
    { period: "3", rate: "18", label: "3 Months - 18% APY" },
    { period: "6", rate: "24", label: "6 Months - 24% APY" },
    { period: "12", rate: "30", label: "12 Months - 30% APY" },
  ]

  // Handle transaction confirmation and show success messages
  useEffect(() => {
    if (isConfirmed && lastTransactionData && hash) {
      const { type, data } = lastTransactionData
      
      switch (type) {
        case 'stake':
          toast({
            title: "Stake created successfully! 🎉",
            description: `Successfully staked ${data.amount} ${data.token} for ${data.lockPeriod} months`,
            variant: "default",
          })
          setIsStakeModalOpen(false)
          setStakeAmount("")
          setSelectedPlan("")
          break
          
        case 'approve':
          toast({
            title: "Approval successful! ✅",
            description: `${data.token} spending approved. You can now proceed with staking.`,
            variant: "default",
          })
          break
          
        case 'claim':
          toast({
            title: "Stake claimed successfully! 💰",
            description: `Successfully claimed your stake and rewards`,
            variant: "default",
          })
          break
      }
      
      // Clear transaction data
      setLastTransactionData(null)
    }
  }, [isConfirmed, lastTransactionData, hash, toast])

  // Show error messages
  useEffect(() => {
    if (error) {
      toast({
        title: "Transaction Failed",
        description: error.message || "An error occurred. Please try again.",
        variant: "destructive",
      })
    }
  }, [error, toast])

  const handleStake = async () => {
    if (!stakeAmount || !selectedPlan) {
      toast({
        title: "Error",
        description: "Please fill in all fields",
        variant: "destructive",
      })
      return
    }

    if (Number(stakeAmount) < 100) {
      toast({
        title: "Error",
        description: "Minimum stake amount is $100",
        variant: "destructive",
      })
      return
    }

    try {
      const currentAllowance = selectedToken === "USDT" ? usdtAllowance : usdcAllowance
      const currentBalance = selectedToken === "USDT" ? usdtBalance : usdcBalance

      // Check if user has enough balance
      if (Number(currentBalance) < Number(stakeAmount)) {
        toast({
          title: "Insufficient Balance",
          description: `You don't have enough ${selectedToken} to stake`,
          variant: "destructive",
        })
        return
      }

      // Check if approval is needed
      if (Number(currentAllowance) < Number(stakeAmount)) {
        toast({
          title: "Approval required",
          description: "Please approve the token spending first",
        })
        
        const result = await approveToken(selectedToken, stakeAmount)
        setLastTransactionData({
          type: 'approve',
          data: { token: selectedToken, amount: stakeAmount }
        })
        return
      }

      toast({
        title: "Creating stake...",
        description: "Please confirm the transaction in your wallet",
      })

      const result = await createStake(selectedToken, stakeAmount, Number(selectedPlan))
      setLastTransactionData({
        type: 'stake',
        data: { token: selectedToken, amount: stakeAmount, lockPeriod: selectedPlan }
      })

    } catch (error: any) {
      console.error("Staking error:", error)
      toast({
        title: "Error",
        description: error.message || "Failed to create stake. Please try again.",
        variant: "destructive",
      })
    }
  }

  const handleClaim = async (stakeId: number) => {
    try {
      toast({
        title: "Claiming stake...",
        description: "Please confirm the transaction in your wallet",
      })
      
      const result = await claimStake(stakeId)
      setLastTransactionData({
        type: 'claim',
        data: { stakeId }
      })
      
    } catch (error: any) {
      toast({
        title: "Error",
        description: error.message || "Failed to claim stake. Please try again.",
        variant: "destructive",
      })
    }
  }

  const formatDate = (timestamp: bigint) => {
    return new Date(Number(timestamp) * 1000).toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
    })
  }

  const getTokenSymbol = (tokenAddress: string) => {
    return tokenAddress.toLowerCase() === "******************************************" ? "USDT" : "USDC"
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-white via-amber-50 to-amber-100 dark:from-slate-900 dark:via-purple-900 dark:to-slate-900 pb-safe-bottom">
      {/* Header */}
      <header className="sticky top-0 z-50 border-b border-amber-200/50 dark:border-slate-700/50 bg-white/50 dark:bg-slate-900/50 backdrop-blur-sm pt-safe-top">
        <div className="flex items-center justify-between px-4 sm:px-6 py-3 sm:py-4">
          <div className="flex items-center gap-2 sm:gap-4">
            <div className="flex items-center gap-2 sm:gap-3">
              <Image
                src="/logo.png"
                alt="Majestic Capital Logo"
                width={32}
                height={32}
                className="rounded-full object-cover sm:w-10 sm:h-10"
                priority
                onError={(e) => {
                  console.log('Logo failed to load, using fallback');
                  e.currentTarget.src = '/placeholder-logo.png';
                }}
              />
              <span className="text-amber-900 dark:text-white font-semibold text-base sm:text-lg hidden xs:block">
                Majestic Capital
              </span>
              <span className="text-amber-900 dark:text-white font-semibold text-base sm:text-lg xs:hidden">
                MC
              </span>
            </div>
            <Button
              variant="ghost"
              size="sm"
              className="text-slate-400 hover:text-white lg:hidden p-2 min-w-[44px] min-h-[44px]"
              onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
            >
              <Menu className="h-5 w-5" />
            </Button>
          </div>

          <div className="flex items-center gap-1 sm:gap-2 md:gap-4">
            <div className="hidden sm:block">
              <ConnectButton />
            </div>
            <div className="sm:hidden">
              <ConnectButton />
            </div>
            <div className="hidden sm:block">
              <ThemeToggle />
            </div>
            <Button
              variant="ghost"
              size="sm"
              className="text-amber-700 dark:text-slate-400 hover:text-amber-800 dark:hover:text-white hidden md:flex p-2 min-w-[44px] min-h-[44px]"
            >
              <User className="h-5 w-5" />
              <ChevronDown className="h-4 w-4 ml-1" />
            </Button>
          </div>
        </div>
      </header>

      <div className="flex relative">
        {/* Mobile Sidebar Overlay */}
        {isMobileMenuOpen && (
          <div
            className="fixed inset-0 bg-black/50 z-40 lg:hidden"
            onClick={() => setIsMobileMenuOpen(false)}
          />
        )}

        {/* Sidebar */}
        <aside className={`
          fixed lg:static inset-y-0 left-0 z-50 w-64
          bg-white/95 dark:bg-slate-900/95 backdrop-blur-sm
          border-r border-amber-200/50 dark:border-slate-700/50
          min-h-[calc(100vh-73px)] lg:min-h-[calc(100vh-73px)]
          transform transition-transform duration-300 ease-in-out
          ${isMobileMenuOpen ? 'translate-x-0' : '-translate-x-full lg:translate-x-0'}
          lg:w-64
        `}>
          <nav className="p-4 space-y-2 pt-6 lg:pt-4">
            {sidebarItems.map((item, index) => (
              item.href ? (
                <Link key={index} href={item.href}>
                  <Button
                    variant={item.active ? "secondary" : "ghost"}
                    className={`w-full justify-start gap-3 min-h-[48px] text-left ${
                      item.active
                        ? "bg-amber-200 dark:bg-slate-800 text-amber-900 dark:text-white"
                        : "text-amber-700 dark:text-slate-400 hover:text-amber-900 dark:hover:text-white hover:bg-amber-100/50 dark:hover:bg-slate-800/50"
                    }`}
                    onClick={() => setIsMobileMenuOpen(false)}
                  >
                    <item.icon className="h-5 w-5 flex-shrink-0" />
                    <span className="truncate">{item.label}</span>
                  </Button>
                </Link>
              ) : (
                <Button
                  key={index}
                  variant={item.active ? "secondary" : "ghost"}
                  className={`w-full justify-start gap-3 min-h-[48px] text-left ${
                    item.active
                      ? "bg-amber-200 dark:bg-slate-800 text-amber-900 dark:text-white"
                      : "text-amber-700 dark:text-slate-400 hover:text-amber-900 dark:hover:text-white hover:bg-amber-100/50 dark:hover:bg-slate-800/50"
                  }`}
                  onClick={() => setIsMobileMenuOpen(false)}
                >
                  <item.icon className="h-5 w-5 flex-shrink-0" />
                  <span className="truncate">{item.label}</span>
                </Button>
              )
            ))}
          </nav>

          {/* Copyright Notice */}
          <div className="absolute bottom-4 left-4 right-4">
            <div className="text-center py-3 border-t border-amber-200/30 dark:border-slate-700/30">
              <p className="text-xs text-amber-600 dark:text-slate-500">
                © 2024 Majestic Capital
              </p>
              <p className="text-xs text-amber-500 dark:text-slate-600 mt-1">
                All rights reserved
              </p>
            </div>
          </div>
        </aside>

        {/* Main Content */}
        <main className="flex-1 p-4 sm:p-6 lg:ml-0 w-full lg:w-auto">
          <div className="max-w-6xl mx-auto space-y-4 sm:space-y-6">
            <div className="flex items-center justify-between">
              <h1 className="text-2xl sm:text-3xl font-bold text-amber-900 dark:text-white">My Assets</h1>
              <div className="sm:hidden">
                <ThemeToggle />
              </div>
            </div>

            {/* Asset Overview Cards */}
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6">
              <Card className="bg-white/50 dark:bg-slate-800/50 border-amber-200 dark:border-slate-700 backdrop-blur-sm">
                <CardHeader className="pb-2 sm:pb-3">
                  <CardTitle className="text-amber-700 dark:text-slate-300 text-sm font-medium">Total Assets</CardTitle>
                </CardHeader>
                <CardContent className="pt-2">
                  <div className="flex items-center gap-3">
                    <div className="w-10 h-10 sm:w-12 sm:h-12 rounded-lg bg-gradient-to-br from-amber-400 to-amber-600 dark:from-blue-500 dark:to-blue-700 flex items-center justify-center shadow-lg flex-shrink-0">
                      <TrendingUp className="w-5 h-5 sm:w-6 sm:h-6 text-white" />
                    </div>
                    <div className="min-w-0 flex-1">
                      <span className="text-2xl sm:text-3xl font-bold text-amber-900 dark:text-white block truncate">${totalAssets}</span>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card className="bg-white/50 dark:bg-slate-800/50 border-amber-200 dark:border-slate-700 backdrop-blur-sm">
                <CardHeader className="pb-2 sm:pb-3">
                  <CardTitle className="text-amber-700 dark:text-slate-300 text-sm font-medium">
                    Available Assets
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-3 sm:space-y-4 pt-2">
                  <div className="flex items-center gap-3">
                    <div className="w-10 h-10 sm:w-12 sm:h-12 rounded-lg bg-gradient-to-br from-green-400 to-green-600 dark:from-emerald-500 dark:to-emerald-700 flex items-center justify-center shadow-lg flex-shrink-0">
                      <Wallet className="w-5 h-5 sm:w-6 sm:h-6 text-white" />
                    </div>
                    <div className="min-w-0 flex-1">
                      <span className="text-2xl sm:text-3xl font-bold text-amber-900 dark:text-white block truncate">${availableAssets}</span>
                    </div>
                  </div>
                  {isConnected && (
                    <div className="text-xs text-slate-400 space-y-1 pl-0 sm:pl-0">
                      <div className="flex justify-between">
                        <span>USDT:</span>
                        <span>{Number(usdtBalance).toFixed(2)}</span>
                      </div>
                      <div className="flex justify-between">
                        <span>USDC:</span>
                        <span>{Number(usdcBalance).toFixed(2)}</span>
                      </div>
                    </div>
                  )}
                </CardContent>
              </Card>

              <Card className="bg-white/50 dark:bg-slate-800/50 border-amber-200 dark:border-slate-700 backdrop-blur-sm">
                <CardHeader className="pb-2 sm:pb-3">
                  <CardTitle className="text-amber-700 dark:text-slate-300 text-sm font-medium">
                    Staked Tokens
                  </CardTitle>
                </CardHeader>
                <CardContent className="pt-2">
                  <div className="flex items-center gap-3">
                    <div className="w-10 h-10 sm:w-12 sm:h-12 rounded-lg bg-gradient-to-br from-purple-400 to-purple-600 dark:from-purple-500 dark:to-purple-700 flex items-center justify-center shadow-lg flex-shrink-0">
                      <Lock className="w-5 h-5 sm:w-6 sm:h-6 text-white" />
                    </div>
                    <div className="min-w-0 flex-1">
                      <span className="text-2xl sm:text-3xl font-bold text-amber-900 dark:text-white block truncate">${stakedTokens}</span>
                    </div>
                  </div>
                  {isConnected && userStakes.length > 0 && (
                    <div className="text-xs text-slate-400 mt-2">
                      {userStakes.length} active stake{userStakes.length !== 1 ? 's' : ''}
                    </div>
                  )}
                </CardContent>
              </Card>
            </div>

            {/* Transaction Status Banner */}
            {(isPending || isConfirming) && (
              <Card className="bg-blue-50 dark:bg-blue-900/20 border-blue-200 dark:border-blue-700">
                <CardContent className="pt-6">
                  <div className="flex items-center gap-3">
                    <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-blue-600"></div>
                    <span className="text-blue-700 dark:text-blue-300">
                      {isPending && "Waiting for wallet confirmation..."}
                      {isConfirming && "Transaction confirming..."}
                    </span>
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Staking Plans */}
            {isConnected && (
              <>
                <div className="text-center space-y-3 sm:space-y-4">
                  <h2 className="text-xl sm:text-2xl font-bold text-amber-900 dark:text-white">Staking Plans</h2>
                  <p className="text-sm sm:text-base text-amber-700 dark:text-slate-300 px-4 sm:px-0">
                    Choose your preferred staking plan and earn guaranteed returns
                  </p>
                </div>

                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6">
                  {stakingPlans.map((plan) => (
                    <Card
                      key={plan.period}
                      className="bg-white/50 dark:bg-slate-800/50 border-amber-200 dark:border-slate-700 backdrop-blur-sm hover:bg-white/70 dark:hover:bg-slate-800/70 transition-all"
                    >
                      <CardHeader className="text-center">
                        <CardTitle className="text-amber-900 dark:text-white text-xl">{plan.period} Months</CardTitle>
                        <div className="text-3xl font-bold text-gradient bg-gradient-to-r from-amber-400 to-orange-400 bg-clip-text text-transparent">
                          {plan.rate}% APY
                        </div>
                      </CardHeader>
                      <CardContent className="space-y-4">
                        <div className="space-y-2 text-sm text-amber-700 dark:text-slate-300">
                          <div className="flex justify-between">
                            <span>Lock Period:</span>
                            <span className="text-amber-900 dark:text-white">{plan.period} months</span>
                          </div>
                          <div className="flex justify-between">
                            <span>Interest Rate:</span>
                            <span className="text-green-600 dark:text-green-400">{plan.rate}% per year</span>
                          </div>
                          <div className="flex justify-between">
                            <span>Minimum:</span>
                            <span className="text-amber-900 dark:text-white">$100</span>
                          </div>
                        </div>
                        <Dialog open={isStakeModalOpen} onOpenChange={setIsStakeModalOpen}>
                          <DialogTrigger asChild>
                            <Button
                              className="w-full bg-gradient-to-r from-amber-500 to-yellow-500 hover:from-amber-600 hover:to-yellow-600 text-white min-h-[48px] text-base font-medium active:scale-95 transition-transform"
                              onClick={() => setSelectedPlan(plan.period)}
                              disabled={isPending || isConfirming}
                            >
                              Stake Now
                            </Button>
                          </DialogTrigger>
                        </Dialog>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </>
            )}

            {/* Staking Modal */}
            <Dialog open={isStakeModalOpen} onOpenChange={setIsStakeModalOpen}>
              <DialogContent className="bg-white dark:bg-slate-800 border-amber-200 dark:border-slate-700 text-amber-900 dark:text-white max-w-md mx-4 sm:mx-auto max-h-[90vh] overflow-y-auto">
                <DialogHeader className="pb-4">
                  <DialogTitle className="text-lg sm:text-xl">Stake Tokens</DialogTitle>
                </DialogHeader>
                <div className="space-y-4 sm:space-y-6">
                  <div className="space-y-2">
                    <Label htmlFor="token" className="text-sm font-medium">Select Token</Label>
                    <Select value={selectedToken} onValueChange={(value: "USDT" | "USDC") => setSelectedToken(value)}>
                      <SelectTrigger className="bg-amber-50 dark:bg-slate-700 border-amber-200 dark:border-slate-600 text-amber-900 dark:text-white h-12">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent className="bg-slate-700 border-slate-600">
                        <SelectItem value="USDT">USDT (Balance: {Number(usdtBalance).toFixed(2)})</SelectItem>
                        <SelectItem value="USDC">USDC (Balance: {Number(usdcBalance).toFixed(2)})</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="amount" className="text-sm font-medium">Amount to Stake</Label>
                    <Input
                      id="amount"
                      type="number"
                      placeholder="Enter amount (min $100)"
                      value={stakeAmount}
                      onChange={(e) => setStakeAmount(e.target.value)}
                      className="bg-amber-50 dark:bg-slate-700 border-amber-200 dark:border-slate-600 text-amber-900 dark:text-white h-12 text-base"
                    />
                  </div>

                  {stakeAmount && selectedPlan && (
                    <div className="bg-amber-100/50 dark:bg-slate-700/50 p-4 rounded-lg space-y-2">
                      <h4 className="font-semibold text-amber-800 dark:text-amber-400">Staking Summary</h4>
                      <div className="text-sm space-y-1">
                        <div className="flex justify-between">
                          <span>Amount:</span>
                          <span>
                            {stakeAmount} {selectedToken}
                          </span>
                        </div>
                        <div className="flex justify-between">
                          <span>Period:</span>
                          <span>{selectedPlan} months</span>
                        </div>
                        <div className="flex justify-between">
                          <span>APY:</span>
                          <span>{stakingPlans.find((p) => p.period === selectedPlan)?.rate}%</span>
                        </div>
                      </div>
                    </div>
                  )}

                  <Button
                    className="w-full bg-gradient-to-r from-amber-500 to-orange-500 hover:from-amber-600 hover:to-orange-600"
                    onClick={handleStake}
                    disabled={!stakeAmount || Number(stakeAmount) < 100 || isPending || isConfirming}
                  >
                    {isPending || isConfirming ? "Processing..." : "Confirm Stake"}
                  </Button>
                </div>
              </DialogContent>
            </Dialog>

            {/* My Stakes */}
            {isConnected && userStakes.length > 0 && (
              <Card className="bg-white/50 dark:bg-slate-800/50 border-amber-200 dark:border-slate-700 backdrop-blur-sm">
                <CardHeader className="pb-4">
                  <CardTitle className="text-xl sm:text-2xl text-amber-900 dark:text-white">My Stakes</CardTitle>
                </CardHeader>
                <CardContent>
                  {/* Mobile Card Layout */}
                  <div className="block sm:hidden space-y-4">
                    {userStakes.map((stake, index) => (
                      <Card key={index} className="bg-amber-50/50 dark:bg-slate-700/50 border-amber-200/50 dark:border-slate-600/50">
                        <CardContent className="p-4 space-y-3">
                          <div className="flex justify-between items-start">
                            <div>
                              <div className="text-sm text-amber-700 dark:text-slate-400">Amount</div>
                              <div className="font-semibold text-amber-900 dark:text-white">
                                {formatUnits(stake.amount, 6)} {getTokenSymbol(stake.token)}
                              </div>
                            </div>
                            <div className="text-right">
                              <div className="text-sm text-amber-700 dark:text-slate-400">APY</div>
                              <div className="font-semibold text-green-600 dark:text-green-400">
                                {Number(stake.interestRate) / 100}%
                              </div>
                            </div>
                          </div>
                          <div className="grid grid-cols-2 gap-3 text-sm">
                            <div>
                              <div className="text-amber-700 dark:text-slate-400">Start Date</div>
                              <div className="text-amber-900 dark:text-white">{formatDate(stake.startDate)}</div>
                            </div>
                            <div>
                              <div className="text-amber-700 dark:text-slate-400">End Date</div>
                              <div className="text-amber-900 dark:text-white">{formatDate(stake.endDate)}</div>
                            </div>
                            <div>
                              <div className="text-amber-700 dark:text-slate-400">Lock Period</div>
                              <div className="text-amber-900 dark:text-white">
                                {Number(stake.lockPeriod) / (24 * 60 * 60) === 90
                                  ? "3"
                                  : Number(stake.lockPeriod) / (24 * 60 * 60) === 180
                                    ? "6"
                                    : "12"} months
                              </div>
                            </div>
                            <div>
                              <div className="text-amber-700 dark:text-slate-400">Status</div>
                              <div>
                                {stake.claimed ? (
                                  <Badge variant="secondary" className="bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300">
                                    <CheckCircle className="h-3 w-3 mr-1" />
                                    Claimed
                                  </Badge>
                                ) : canClaimStake(stake.endDate) ? (
                                  <Button
                                    size="sm"
                                    onClick={() => handleClaim(index)}
                                    disabled={isPending || isConfirming}
                                    className="bg-green-600 hover:bg-green-700 text-white min-h-[36px]"
                                  >
                                    Claim
                                  </Button>
                                ) : (
                                  <Badge variant="outline" className="border-amber-400 text-amber-700 dark:text-amber-400">
                                    <Clock className="h-3 w-3 mr-1" />
                                    Locked
                                  </Badge>
                                )}
                              </div>
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                    ))}
                  </div>

                  {/* Desktop Table Layout */}
                  <div className="hidden sm:block overflow-x-auto">
                    <table className="w-full">
                      <thead>
                        <tr className="border-b border-slate-700">
                          <th className="text-left py-3 px-4 text-amber-600 dark:text-slate-400 font-medium">Amount</th>
                          <th className="text-left py-3 px-4 text-slate-400 font-medium">Starting Date</th>
                          <th className="text-left py-3 px-4 text-slate-400 font-medium">Lock Period</th>
                          <th className="text-left py-3 px-4 text-slate-400 font-medium">Interest/Year</th>
                          <th className="text-left py-3 px-4 text-slate-400 font-medium">End Date</th>
                          <th className="text-left py-3 px-4 text-slate-400 font-medium">Actions</th>
                        </tr>
                      </thead>
                      <tbody>
                        {userStakes.map((stake, index) => (
                          <tr key={index} className="border-b border-slate-700/50">
                            <td className="py-4 px-4">
                              <div className="flex items-center gap-2">
                                <DollarSign className="h-4 w-4 text-green-400" />
                                <span className="text-amber-900 dark:text-white font-medium">
                                  {formatUnits(stake.amount, 6)} {getTokenSymbol(stake.token)}
                                </span>
                              </div>
                            </td>
                            <td className="py-4 px-4">
                              <div className="flex items-center gap-2">
                                <Calendar className="h-4 w-4 text-blue-400" />
                                <span className="text-amber-700 dark:text-slate-300">
                                  {formatDate(stake.startDate)}
                                </span>
                              </div>
                            </td>
                            <td className="py-4 px-4">
                              <div className="flex items-center gap-2">
                                <Clock className="h-4 w-4 text-orange-400" />
                                <span className="text-amber-900 dark:text-white">
                                  {Number(stake.lockPeriod) / (24 * 60 * 60) === 90
                                    ? "3"
                                    : Number(stake.lockPeriod) / (24 * 60 * 60) === 180
                                      ? "6"
                                      : "12"}{" "}
                                  months
                                </span>
                              </div>
                            </td>
                            <td className="py-4 px-4">
                              <span className="text-green-400 font-medium">{Number(stake.interestRate) / 100}%</span>
                            </td>
                            <td className="py-4 px-4">
                              <span className="text-slate-300">{formatDate(stake.endDate)}</span>
                            </td>
                            <td className="py-4 px-4">
                              {stake.claimed ? (
                                <Badge variant="secondary" className="bg-gray-900/30 text-gray-400 border-gray-700">
                                  <CheckCircle className="h-3 w-3 mr-1" />
                                  Claimed
                                </Badge>
                              ) : canClaimStake(stake.endDate) ? (
                                <Button
                                  size="sm"
                                  onClick={() => handleClaim(index)}
                                  className="bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white"
                                  disabled={isPending || isConfirming}
                                >
                                  Claim
                                </Button>
                              ) : (
                                <Badge variant="secondary" className="bg-blue-900/30 text-blue-400 border-blue-700">
                                  <Clock className="h-3 w-3 mr-1" />
                                  Locked
                                </Badge>
                              )}
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Connect Wallet Prompt */}
            {!isConnected && (
              <Card className="bg-white/50 dark:bg-slate-800/50 border-amber-200 dark:border-slate-700 backdrop-blur-sm">
                <CardContent className="text-center py-12">
                  <Wallet className="h-12 w-12 text-slate-400 mx-auto mb-4" />
                  <h3 className="text-xl font-semibold text-amber-900 dark:text-white mb-2">Connect Your Wallet</h3>
                  <p className="text-amber-700 dark:text-slate-400 mb-6">
                    Connect your wallet to start staking and earning rewards
                  </p>
                  <ConnectButton />
                </CardContent>
              </Card>
            )}
          </div>
        </main>
      </div>
    </div>
  )
}